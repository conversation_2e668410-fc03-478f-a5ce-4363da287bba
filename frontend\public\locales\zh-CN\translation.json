{"app": {"title": "One MCP", "loading": "加载中..."}, "nav": {"dashboard": "仪表盘", "services": "服务", "market": "服务市场", "analytics": "分析", "users": "用户", "profile": "个人资料", "preferences": "偏好设置"}, "auth": {"login": "登录", "logout": "登出", "loginButton": "登录", "logoutButton": "登出"}, "common": {"save": "保存", "cancel": "取消", "close": "关闭", "edit": "编辑", "delete": "删除", "add": "添加", "back": "返回", "next": "下一步", "loading": "加载中...", "error": "错误", "success": "成功", "search": "搜索", "searching": "搜索中...", "configure": "配置", "enable": "启用", "disable": "禁用", "enabled": "已启用", "disabled": "已禁用", "unknown": "未知", "refresh": "刷新", "uninstall": "卸载"}, "batchImport": {"title": "批量导入服务", "placeholder": "{\n  \"mcpServers\": {\n    \"my-stdio-service\": {\n      \"command\": \"npx\",\n      \"args\": [\"-y\", \"some-package\"],\n      \"env\": {\"API_KEY\": \"your_key\"}\n    },\n    \"my-sse-service\": {\n      \"url\": \"http://example.com/events/sse\"\n    },\n    \"my-http-service\": {\n      \"url\": \"http://example.com/mcp?key=your_key\"\n    }\n  }\n}", "import": "导入", "importing": "导入中...", "invalidJsonError": "JSON 格式无效，请检查您的输入。", "processingServices": "正在处理服务...", "importComplete": "导入完成", "importSummary": "导入完成：{{success}} 个成功，{{skipped}} 个跳过，{{failed}} 个失败。", "successMessage": "服务已成功导入，服务列表已刷新。"}, "dashboard": {"title": "仪表盘", "welcome": "欢迎使用 One MCP", "description": "您的 MCP（模型上下文协议）服务集中代理", "content": "通过统一代理界面管理 MCP 服务并监控其性能。", "activeServices": "启用服务", "activeServicesDesc": "已启用的服务数量", "todayRequests": "今日请求", "todayRequestsDesc": "今天的总API请求数", "avgResponseTime": "平均响应时间", "avgResponseTimeDesc": "今天的平均响应时间", "remainingCredits": "剩余积分", "remainingCreditsDesc": "积分功能即将推出", "systemStatus": "系统状态", "systemStatusDesc": "系统运行时间和服务健康概览", "systemUptime": "系统正常运行时间", "totalServices": "总服务数", "healthyServices": "健康服务", "unhealthyServices": "不健康服务", "enabled": "已启用", "quickActions": "快速操作", "quickActionsDesc": "开始使用您的 MCP 平台", "manageServices": "管理服务", "manageServicesDesc": "配置现有服务", "viewAnalytics": "查看分析", "viewAnalyticsDesc": "检查性能指标", "userSettings": "用户设置", "userSettingsDesc": "管理您的账户", "installService": "安装服务", "installServiceDesc": "添加新的 MCP 服务", "recentActivity": "最近活动", "recentActivityDesc": "最新操作和系统事件（静态数据，API开发中）", "time": "时间", "event": "事件", "service": "服务", "status": "状态", "serviceStarted": "服务已启动", "configUpdated": "配置已更新", "apiKeyGenerated": "API密钥已生成", "serviceRestarted": "服务已重启", "searchService": "搜索服务", "analytics": "分析", "system": "系统", "userManagement": "用户管理", "success": "成功", "warning": "警告", "today": "今天", "yesterday": "昨天", "unknown": "未知", "error": "错误", "fetchDataFailed": "获取数据失败"}, "clipboardError": {"accessDenied": "无法访问剪贴板。请确保网站通过 HTTPS 访问，或在浏览器设置中允许剪贴板访问。", "execCommandFailed": "复制功能不可用。请手动选择并复制文本。", "notSupported": "您的浏览器不支持剪贴板操作。请手动选择并复制文本。"}, "services": {"title": "MCP 服务", "description": "描述", "addService": "添加服务", "batchImport": "批量导入", "fromMarket": "从市场安装", "customInstall": "自定义安装", "noServicesFound": "未找到服务。", "viewGrid": "网格视图", "viewList": "列表视图", "allServices": "所有服务", "active": "启用", "inactive": "禁用", "mcpServices": "MCP 服务", "manageAndConfigure": "管理和配置您的多云平台服务", "installFromMarket": "从市场安装", "status": "状态", "serviceName": "服务名称", "version": "版本", "healthStatus": "健康状态", "enabledStatus": "启用状态", "operations": "操作", "configure": "配置", "uninstall": "卸载", "refreshHealthStatus": "刷新健康状态", "uninstallService": "卸载服务", "dailyRequests": "每日请求", "requestsRemaining": "剩余请求", "confirmUninstall": "确认卸载", "confirmUninstallDescription": "您确定要卸载此服务吗？这将删除所有相关配置。", "cancel": "取消"}, "market": {"title": "服务市场", "description": "发现并安装来自各种来源的 MCP 服务", "searchPlaceholder": "搜索 MCP 服务...", "search": "搜索", "searching": "搜索中...", "noServicesFound": "未找到服务。请尝试不同的搜索词。", "searchingServices": "正在搜索服务...", "backToMarketplace": "返回市场", "serviceNotFound": "未找到服务", "serviceNotFoundDesc": "无法找到请求的服务。", "install": "安装", "installing": "安装中...", "installed": "已安装", "version": "版本", "author": "作者", "stars": "星标", "downloads": "下载量", "weeklyDownloads": "周下载量"}, "serviceMarketplace": {"title": "服务市场", "description": "发现并安装来自各种来源的 MCP 服务", "searchPlaceholder": "搜索 MCP 服务...", "searchButton": "搜索", "searching": "正在搜索服务...", "noServicesFound": "未找到服务。请尝试不同的搜索词。", "installing": "安装中...", "installationComplete": "安装完成", "installationFailed": "安装失败"}, "serviceDetails": {"loadingServiceDetails": "加载服务详情中...", "backToMarketplace": "返回市场", "serviceNotFound": "未找到服务", "serviceNotFoundDescription": "无法找到请求的服务。", "by": "作者", "source": "来源", "uninstallService": "卸载服务", "installService": "安装服务", "installingService": "正在安装服务...", "installing": "安装中", "from": "来自", "theServiceWasInstalledSuccessfully": "服务安装成功", "thereWasAProblemDuringInstallation": "安装过程中出现问题", "finish": "完成", "close": "关闭"}, "users": {"title": "用户管理", "description": "管理系统用户及其权限", "searchPlaceholder": "搜索用户...", "addUser": "添加用户", "noUsersFound": "未找到用户。", "id": "ID", "username": "用户名", "displayName": "显示名称", "email": "邮箱", "userRole": "用户角色", "binding": "绑定", "status": "状态", "actions": "操作", "loading": "加载中...", "search": "搜索", "searching": "搜索中...", "edit": "编辑", "delete": "删除", "promoteToAdmin": "设为管理员", "demoteToUser": "设为普通用户", "showing": "显示", "users": "个用户", "previousPage": "上一页", "nextPage": "下一页", "confirmDeleteTitle": "确认删除", "confirmDeleteDescription": "确定要删除此用户吗？此操作不可撤销。", "cancel": "取消", "roles": {"admin": "管理员", "user": "用户"}, "statusTypes": {"active": "启用", "inactive": "禁用"}, "messages": {"deleteSuccess": "用户删除成功", "deleteFailed": "删除用户失败", "promoteSuccess": "用户已设为管理员", "demoteSuccess": "用户已设为普通用户", "enableSuccess": "用户已启用", "disableSuccess": "用户已禁用", "operationFailed": "操作失败"}, "bindings": {"github": "GitHub", "google": "Google", "wechat": "微信", "none": "无"}}, "usersPage": {"title": "用户管理", "description": "管理系统用户账户", "searchPlaceholder": "搜索用户ID、用户名、显示名称或邮箱...", "addUser": "新增用户", "noUsersFound": "没有找到用户", "id": "ID", "username": "用户名", "displayName": "显示名称", "email": "邮箱", "userRole": "用户角色", "binding": "绑定", "status": "状态", "actions": "操作", "loading": "加载中...", "search": "搜索", "searching": "搜索中...", "edit": "编辑", "delete": "删除", "promoteToAdmin": "设为管理员", "demoteToUser": "设为普通用户", "showing": "显示", "users": "个用户", "previousPage": "上一页", "nextPage": "下一页", "confirmDeleteTitle": "确认删除", "confirmDeleteDescription": "确定要删除此用户吗？此操作不可撤销。", "cancel": "取消", "roles": {"superAdmin": "超级管理员", "admin": "管理员", "user": "普通用户", "unknown": "未知"}, "bindings": {"none": "无"}, "messages": {"operationSuccess": "操作成功", "operationFailed": "操作失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "userDeleted": "用户已成功删除", "userPromoted": "用户已设为管理员", "userDemoted": "用户已设为普通用户", "userEnabled": "用户已启用", "userDisabled": "用户已禁用", "unknownError": "未知错误", "networkError": "网络错误"}}, "userDialog": {"addTitle": "新增用户", "editTitle": "编辑用户", "addDescription": "创建一个新用户账户。", "editDescription": "修改用户的用户名、显示名称和密码。", "form": {"username": "用户名", "displayName": "显示名称", "password": "密码", "passwordPlaceholder": "输入密码", "passwordEditPlaceholder": "留空则不修改密码"}, "actions": {"cancel": "取消", "save": "保存", "saving": "保存中..."}, "messages": {"createSuccess": "创建成功", "updateSuccess": "更新成功", "createFailed": "创建失败", "updateFailed": "更新失败", "userCreated": "用户已成功创建", "userUpdated": "用户信息已更新", "unknownError": "未知错误", "networkError": "网络错误"}}, "profile": {"title": "个人资料", "description": "管理您的个人信息和账户设置", "personalInfo": "个人信息", "personalInfoDesc": "更新您的个人详细信息", "accountSecurity": "账户安全", "accountSecurityDesc": "管理您的密码和API访问", "apiAccess": "API 访问", "apiAccessDesc": "管理您的API密钥以进行外部访问", "form": {"username": "用户名", "email": "邮箱", "displayName": "显示名称", "currentPassword": "当前密码", "newPassword": "新密码", "confirmPassword": "确认新密码", "apiKey": "API 密钥", "loginMethod": "登录方式"}, "loginMethods": {"password": "密码", "github": "GitHub", "google": "Google", "wechat": "微信"}, "actions": {"saveProfile": "保存资料", "changePassword": "修改密码", "refreshApiKey": "刷新 API 密钥", "showApiKey": "显示 API 密钥", "hideApiKey": "隐藏 API 密钥", "saving": "保存中..."}, "messages": {"profileUpdateSuccess": "资料更新成功", "profileUpdateFailed": "资料更新失败", "passwordChangeSuccess": "密码修改成功", "passwordChangeFailed": "密码修改失败", "passwordMismatch": "新密码和确认密码不匹配", "currentPasswordIncorrect": "当前密码不正确", "oauthUserCannotChangePassword": "OAuth 用户无法修改密码", "apiKeyRefreshSuccess": "API 密钥刷新成功", "apiKeyRefreshFailed": "API 密钥刷新失败", "fetchUserInfoFailed": "获取用户信息失败"}, "notes": {"oauthPasswordNote": "OAuth 用户无法修改密码。请使用您的 OAuth 提供商来管理您的账户。", "oauthReadOnlyInfo": "通过第三方登录的用户信息（只读）", "apiKeyTitle": "注意事项", "apiKeyNote1": "重新生成后，旧的 API Key 将立即失效", "apiKeyNote2": "请及时更新您的应用程序配置", "apiKeyNote3": "请妥善保管您的 API Key"}}, "preferences": {"title": "偏好设置", "description": "管理您的系统配置和登录选项", "general": "通用设置", "generalDesc": "配置系统的基本设置", "oauth": "OAuth 配置", "oauthDesc": "配置第三方登录提供商", "github": "配置 GitHub OAuth App", "google": "配置 Google OAuth App", "githubDesc": "用以支持通过 GitHub 进行登录注册", "githubLinkText": "点击此处管理你的 GitHub OAuth App", "googleDesc": "用以支持通过 Google 进行登录注册", "googleLinkText": "点击此处管理你的 Google OAuth App", "form": {"serverAddress": "服务器地址", "githubClientId": "GitHub 客户端 ID", "githubClientSecret": "GitHub 客户端密钥", "googleClientId": "Google 客户端 ID", "googleClientSecret": "Google 客户端密钥", "enableGithubOAuth": "允许通过 GitHub 账户登录 & 注册", "enableGoogleOAuth": "允许通过 Google 账户登录 & 注册", "githubClientIdPlaceholder": "输入 GitHub Client ID", "githubClientSecretPlaceholder": "输入 GitHub Client Secret", "googleClientIdPlaceholder": "输入 Google Client ID", "googleClientSecretPlaceholder": "输入 Google Client Secret"}, "actions": {"save": "保存", "saving": "保存中..."}, "rateLimit": {"title": "速率限制配置", "description": "配置系统的速率限制参数", "globalApi": "全局 API 速率限制", "globalWeb": "全局 Web 速率限制", "requestsPerPeriod": "时间段内请求数", "periodSeconds": "时间段（秒）"}, "success": "成功", "error": "错误", "rateLimitUpdated": "速率限制配置已更新", "rateLimitUpdateFailed": "速率限制配置更新失败", "instructions": {"githubHomepage": "Homepage URL 填", "githubCallback": "，Authorization callback URL 填", "googleOrigins": "Authorized JavaScript origins 填", "googleRedirect": "，Authorized redirect URIs 填"}, "messages": {"saveSuccess": "设置保存成功", "saveFailed": "设置保存失败", "githubOAuthEnabled": "GitHub OAuth 已启用", "githubOAuthDisabled": "GitHub OAuth 已禁用", "googleOAuthEnabled": "Google OAuth 已启用", "googleOAuthDisabled": "Google OAuth 已禁用", "githubOAuthSaved": "GitHub OAuth 配置已保存", "googleOAuthSaved": "Google OAuth 配置已保存"}}, "analytics": {"title": "分析", "description": "监控系统性能和使用统计", "usageStatistics": "使用统计", "serviceUtilization": "服务利用率", "aggregatedPerformanceOverview": "已启用服务的聚合性能概览。", "sortedBy": "按", "todayRequests": "今日请求", "highToLow": "从高到低", "lowToHigh": "从低到高", "clickColumnHeader": "点击列标题可", "changeSortOrder": "更改排序。", "loadingStatistics": "加载统计数据中...", "error": "错误", "summaryOfServiceUsage": "您的服务使用摘要。", "service": "服务", "status": "状态", "todayAvgLatency": "今日平均延迟（毫秒）", "noEnabledServicesWithUsageStatisticsAvailableYet": "暂无启用服务的使用统计数据。", "enabled": "已启用"}, "language": {"select": "选择语言", "current": "当前语言"}, "envVarModal": {"title": "填写所需环境变量", "description": "安装该服务需要以下环境变量，请补充完整后继续。", "placeholder": "请输入 {{varName}}", "errorRequired": "请填写 {{varName}}", "cancel": "取消", "confirm": "确认"}, "customServiceModal": {"title": "创建自定义服务", "description": "填写以下信息创建一个自定义MCP服务", "form": {"serviceName": "服务名称", "serviceNamePlaceholder": "输入服务名称", "serviceType": "服务类型", "serviceTypePlaceholder": "选择服务类型", "command": "执行命令", "commandPlaceholder": "npx 或 uvx", "arguments": "命令参数", "argumentsPlaceholder": "--arg1 value1\n--arg2 value2", "environments": "环境变量", "environmentsPlaceholder": "VARIABLE_NAME=value\nANOTHER_VAR=another_value", "serverUrl": "服务器URL", "serverUrlPlaceholder": "https://your-server.com/api", "requestHeaders": "请求头", "requestHeadersPlaceholder": "Content-Type=application/json\nAuthorization=Bearer token\n..."}, "serviceTypes": {"stdio": "标准输入/输出 (stdio)", "sse": "服务器发送事件 (sse)", "streamableHttp": "可流式传输的HTTP (streamableHttp)"}, "status": {"validating": "验证中", "validatingDescription": "正在检查表单数据...", "validationSuccess": "验证成功!", "validationSuccessDescription": "正在创建服务...", "creating": "创建中", "creatingDescription": "正在保存服务配置..."}, "actions": {"cancel": "取消", "createService": "创建服务", "validating": "验证中...", "validationSuccess": "验证成功", "creating": "创建中..."}, "messages": {"createSuccess": "创建成功", "createSuccessDescription": "服务 {{serviceName}} 已成功创建", "createFailed": "创建失败", "unknownError": "未知错误", "commandMustStartWith": "命令必须以 npx 或 uvx 开头", "installationSubmitted": "安装已提交", "installationSubmittedDescription": "服务 {{serviceName}} 安装任务已提交，请稍候", "installationSuccess": "安装成功", "installationSuccessDescription": "服务 {{serviceName}} 已成功安装", "installationFailed": "安装失败", "installationFailedDescription": "服务 {{serviceName}} 安装失败: {{error}}", "installationTimeout": "安装超时", "installationTimeoutDescription": "服务 {{serviceName}} 安装超时，请手动检查状态", "statusCheckTimeout": "状态检查超时", "statusCheckTimeoutDescription": "无法获取服务 {{serviceName}} 的安装状态", "statusCheckFailed": "状态检查失败", "statusCheckFailedDescription": "检查服务 {{serviceName}} 安装状态时出错", "parseCommandFailed": "无法从命令中解析包管理器或包名称。命令必须是 \"npx\" 或 \"uvx\"，并且需要在命令参数中指定包名。"}}, "serviceConfigModal": {"title": "服务配置", "description": "调整此服务的设置。完成后点击保存。", "sections": {"environmentVariables": "环境变量", "noEnvironmentVariables": "此服务没有环境变量。", "serviceEndpoints": "服务端点", "sseEndpoint": "SSE端点", "httpEndpoint": "HTTP端点", "sseConfigJson": "SSE配置 (JSON格式)", "httpConfigJson": "HTTP配置 (JSON格式)", "rpdLimit": "每日请求次数限制 (RPD)", "currentLimit": "当前限制:", "unlimited": "(无限制)", "requestsPerDay": "次/天"}, "actions": {"save": "保存", "saving": "保存中...", "close": "关闭", "copySSEConfig": "复制SSE配置", "copyHTTPConfig": "复制HTTP配置", "limitPlaceholder": "0 表示不限制"}, "messages": {"sseConfigCopied": "SSE配置已复制", "sseConfigCopiedDesc": "SSE端点的MCP服务器配置已复制到剪贴板", "httpConfigCopied": "HTTP配置已复制", "httpConfigCopiedDesc": "HTTP端点的MCP服务器配置已复制到剪贴板", "copyFailed": "复制失败", "clipboardNotSupported": "剪贴板功能不可用", "manualCopyHint": "由于安全限制，自动复制功能在HTTP环境下不可用。请使用下方的文本区域手动复制配置。", "selectAllHint": "点击文本区域可全选内容，然后使用 Ctrl+C (或 Cmd+C) 复制", "updateSuccess": "更新成功", "rpdLimitUpdated": "每日请求限制已更新为 {{limit}}", "unlimitedValue": "无限制", "updateFailed": "更新失败", "saveFailed": "保存失败", "rpdUpdateError": "更新RPD限制时发生错误"}}}