{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "rm -rf dist && tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@headlessui/react": "^2.2.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.507.0", "phosphor-react": "^1.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.2", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.3", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@playwright/test": "^1.52.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/axios": "^0.9.36", "@types/node": "^24.0.10", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "autoprefixer": "^10.4.16", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "postcss": "^8.4.31", "shadcn": "^2.5.0", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.1.4"}, "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "eslint.config.js", "keywords": [], "author": "", "license": "ISC"}