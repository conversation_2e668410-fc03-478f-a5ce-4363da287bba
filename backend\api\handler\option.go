package handler

import (
	"encoding/json"
	"net/http"
	"one-mcp/backend/common"
	"one-mcp/backend/library/proxy"
	"one-mcp/backend/model"
	"one-mcp/backend/service"

	"github.com/gin-gonic/gin"
)

func GetOptions(c *gin.Context) {
	options, err := model.OptionDB.All()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    options,
	})
	return
}

func UpdateOption(c *gin.Context) {
	var option model.Option
	err := json.NewDecoder(c.Request.Body).Decode(&option)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	switch option.Key {
	case "ServerAddress":
		proxy.ClearSSEProxyCache()
	case "GitHubOAuthEnabled":
		if option.Value == "true" && common.GetGitHubClientId() == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用 GitHub OAuth，请先填入 GitHub Client ID 以及 GitHub Client Secret！",
			})
			return
		}
	case "GoogleOAuthEnabled":
		if option.Value == "true" && common.GetGoogleClientId() == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用 Google OAuth，请先填入 Google Client ID 以及 Google Client Secret！",
			})
			return
		}
	case "WeChatAuthEnabled":
		if option.Value == "true" && common.GetWeChatServerAddress() == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用微信登录，请先填入微信登录相关配置信息！",
			})
			return
		}
	case "TurnstileCheckEnabled":
		if option.Value == "true" && common.GetTurnstileSiteKey() == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用 Turnstile 校验，请先填入 Turnstile 校验相关配置信息！",
			})
			return
		}
	}
	err = service.UpdateOption(option.Key, option.Value)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

// GetRateLimitConfig returns current rate limit configuration
func GetRateLimitConfig(c *gin.Context) {
	config := gin.H{
		"GlobalApiRateLimitNum":        common.GlobalApiRateLimitNum,
		"GlobalApiRateLimitDuration":   common.GlobalApiRateLimitDuration,
		"GlobalWebRateLimitNum":        common.GlobalWebRateLimitNum,
		"GlobalWebRateLimitDuration":   common.GlobalWebRateLimitDuration,
		"UploadRateLimitNum":           common.UploadRateLimitNum,
		"UploadRateLimitDuration":      common.UploadRateLimitDuration,
		"DownloadRateLimitNum":         common.DownloadRateLimitNum,
		"DownloadRateLimitDuration":    common.DownloadRateLimitDuration,
		"CriticalRateLimitNum":         common.CriticalRateLimitNum,
		"CriticalRateLimitDuration":    common.CriticalRateLimitDuration,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// SetRateLimitConfig updates rate limit configuration
func SetRateLimitConfig(c *gin.Context) {
	var config map[string]interface{}
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request format",
		})
		return
	}

	// Update rate limit values
	if val, ok := config["GlobalApiRateLimitNum"]; ok {
		if num, ok := val.(float64); ok {
			common.GlobalApiRateLimitNum = int(num)
		}
	}
	if val, ok := config["GlobalApiRateLimitDuration"]; ok {
		if duration, ok := val.(float64); ok {
			common.GlobalApiRateLimitDuration = int64(duration)
		}
	}
	if val, ok := config["GlobalWebRateLimitNum"]; ok {
		if num, ok := val.(float64); ok {
			common.GlobalWebRateLimitNum = int(num)
		}
	}
	if val, ok := config["GlobalWebRateLimitDuration"]; ok {
		if duration, ok := val.(float64); ok {
			common.GlobalWebRateLimitDuration = int64(duration)
		}
	}
	if val, ok := config["UploadRateLimitNum"]; ok {
		if num, ok := val.(float64); ok {
			common.UploadRateLimitNum = int(num)
		}
	}
	if val, ok := config["UploadRateLimitDuration"]; ok {
		if duration, ok := val.(float64); ok {
			common.UploadRateLimitDuration = int64(duration)
		}
	}
	if val, ok := config["DownloadRateLimitNum"]; ok {
		if num, ok := val.(float64); ok {
			common.DownloadRateLimitNum = int(num)
		}
	}
	if val, ok := config["DownloadRateLimitDuration"]; ok {
		if duration, ok := val.(float64); ok {
			common.DownloadRateLimitDuration = int64(duration)
		}
	}
	if val, ok := config["CriticalRateLimitNum"]; ok {
		if num, ok := val.(float64); ok {
			common.CriticalRateLimitNum = int(num)
		}
	}
	if val, ok := config["CriticalRateLimitDuration"]; ok {
		if duration, ok := val.(float64); ok {
			common.CriticalRateLimitDuration = int64(duration)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Rate limit configuration updated successfully",
	})
}
