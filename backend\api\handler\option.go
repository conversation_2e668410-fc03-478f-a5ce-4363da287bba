package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"one-mcp/backend/common"
	"one-mcp/backend/library/proxy"
	"one-mcp/backend/model"
	"one-mcp/backend/service"

	"github.com/gin-gonic/gin"
)

func GetOptions(c *gin.Context) {
	options, err := model.OptionDB.All()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    options,
	})
	return
}

func UpdateOption(c *gin.Context) {
	var option model.Option
	err := json.NewDecoder(c.Request.Body).Decode(&option)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	switch option.Key {
	case "ServerAddress":
		proxy.ClearSSEProxyCache()
	case "GitHubOAuthEnabled":
		if option.Value == "true" && common.GetGitHubClientId() == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用 GitHub OAuth，请先填入 GitHub Client ID 以及 GitHub Client Secret！",
			})
			return
		}
	case "GoogleOAuthEnabled":
		if option.Value == "true" && common.GetGoogleClientId() == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用 Google OAuth，请先填入 Google Client ID 以及 Google Client Secret！",
			})
			return
		}
	case "WeChatAuthEnabled":
		if option.Value == "true" && common.GetWeChatServerAddress() == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用微信登录，请先填入微信登录相关配置信息！",
			})
			return
		}
	case "TurnstileCheckEnabled":
		if option.Value == "true" && common.GetTurnstileSiteKey() == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用 Turnstile 校验，请先填入 Turnstile 校验相关配置信息！",
			})
			return
		}
	}
	err = service.UpdateOption(option.Key, option.Value)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

// GetRateLimitConfig returns current rate limit configuration
func GetRateLimitConfig(c *gin.Context) {
	config := gin.H{
		"GlobalApiRateLimitNum":        common.GetGlobalApiRateLimitNum(),
		"GlobalApiRateLimitDuration":   common.GetGlobalApiRateLimitDuration(),
		"GlobalWebRateLimitNum":        common.GetGlobalWebRateLimitNum(),
		"GlobalWebRateLimitDuration":   common.GetGlobalWebRateLimitDuration(),
		"UploadRateLimitNum":           common.GetUploadRateLimitNum(),
		"UploadRateLimitDuration":      common.GetUploadRateLimitDuration(),
		"DownloadRateLimitNum":         common.GetDownloadRateLimitNum(),
		"DownloadRateLimitDuration":    common.GetDownloadRateLimitDuration(),
		"CriticalRateLimitNum":         common.GetCriticalRateLimitNum(),
		"CriticalRateLimitDuration":    common.GetCriticalRateLimitDuration(),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    config,
	})
}

// UpdateRateLimitConfig updates rate limit configuration
func UpdateRateLimitConfig(c *gin.Context) {
	var config map[string]interface{}
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	// Validate and update each configuration
	for key, value := range config {
		var strValue string
		switch v := value.(type) {
		case float64:
			strValue = fmt.Sprintf("%.0f", v)
		case int:
			strValue = fmt.Sprintf("%d", v)
		case string:
			strValue = v
		default:
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": fmt.Sprintf("无效的值类型: %s", key),
			})
			return
		}

		// Validate the key is a valid rate limit configuration
		validKeys := map[string]bool{
			"GlobalApiRateLimitNum":        true,
			"GlobalApiRateLimitDuration":   true,
			"GlobalWebRateLimitNum":        true,
			"GlobalWebRateLimitDuration":   true,
			"UploadRateLimitNum":           true,
			"UploadRateLimitDuration":      true,
			"DownloadRateLimitNum":         true,
			"DownloadRateLimitDuration":    true,
			"CriticalRateLimitNum":         true,
			"CriticalRateLimitDuration":    true,
		}

		if !validKeys[key] {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": fmt.Sprintf("无效的配置项: %s", key),
			})
			return
		}

		// Update the option in database
		if err := service.UpdateOption(key, strValue); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": fmt.Sprintf("更新配置失败: %s", err.Error()),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "速率限制配置更新成功",
	})
}
