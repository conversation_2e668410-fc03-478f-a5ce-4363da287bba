package common

import "strconv"

// GetGitHubClientId 获取GitHub客户端ID
func GetGitHubClientId() string {
	return OptionMap["GitHubClientId"]
}

// GetGitHubClientSecret 获取GitHub客户端密钥
func GetGitHubClientSecret() string {
	return OptionMap["GitHubClientSecret"]
}

// GetGitHubOAuthEnabled 获取GitHub OAuth是否启用
func GetGitHubOAuthEnabled() bool {
	return OptionMap["GitHubOAuthEnabled"] == "true"
}

// GetGoogleClientId 获取Google客户端ID
func GetGoogleClientId() string {
	return OptionMap["GoogleClientId"]
}

// GetGoogleClientSecret 获取Google客户端密钥
func GetGoogleClientSecret() string {
	return OptionMap["GoogleClientSecret"]
}

// GetGoogleOAuthEnabled 获取Google OAuth是否启用
func GetGoogleOAuthEnabled() bool {
	return OptionMap["GoogleOAuthEnabled"] == "true"
}

// GetServerAddress 获取服务器地址
func GetServerAddress() string {
	return OptionMap["ServerAddress"]
}

// GetSystemName 获取系统名称
func GetSystemName() string {
	return OptionMap["SystemName"]
}

// GetFooter 获取页脚信息
func GetFooter() string {
	return OptionMap["Footer"]
}

// GetHomePageLink 获取首页链接
func GetHomePageLink() string {
	return OptionMap["HomePageLink"]
}

// GetRegisterEnabled 获取注册是否启用
func GetRegisterEnabled() bool {
	return OptionMap["RegisterEnabled"] == "true"
}

// GetEmailVerificationEnabled 获取邮箱验证是否启用
func GetEmailVerificationEnabled() bool {
	return OptionMap["EmailVerificationEnabled"] == "true"
}

// GetWeChatAuthEnabled 获取微信认证是否启用
func GetWeChatAuthEnabled() bool {
	return OptionMap["WeChatAuthEnabled"] == "true"
}

// GetWeChatServerAddress 获取微信服务器地址
func GetWeChatServerAddress() string {
	return OptionMap["WeChatServerAddress"]
}

// GetWeChatServerToken 获取微信服务器令牌
func GetWeChatServerToken() string {
	return OptionMap["WeChatServerToken"]
}

// GetWeChatAccountQRCodeImageURL 获取微信账号二维码图片URL
func GetWeChatAccountQRCodeImageURL() string {
	return OptionMap["WeChatAccountQRCodeImageURL"]
}

// GetTurnstileCheckEnabled 获取Turnstile检查是否启用
func GetTurnstileCheckEnabled() bool {
	return OptionMap["TurnstileCheckEnabled"] == "true"
}

// GetTurnstileSiteKey 获取Turnstile站点密钥
func GetTurnstileSiteKey() string {
	return OptionMap["TurnstileSiteKey"]
}

// GetTurnstileSecretKey 获取Turnstile秘密密钥
func GetTurnstileSecretKey() string {
	return OptionMap["TurnstileSecretKey"]
}

// GetSMTPServer 获取SMTP服务器
func GetSMTPServer() string {
	return OptionMap["SMTPServer"]
}

// GetSMTPAccount 获取SMTP账号
func GetSMTPAccount() string {
	return OptionMap["SMTPAccount"]
}

// GetSMTPToken 获取SMTP令牌
func GetSMTPToken() string {
	return OptionMap["SMTPToken"]
}

// GetEnableGzip checks if gzip compression should be enabled.
// Defaults to true if the option is not explicitly set to "false".
func GetEnableGzip() bool {
	// We treat any value other than "false" as true for safety.
	return OptionMap["EnableGzip"] != "false"
}

// Rate Limit Configuration Functions

// GetGlobalApiRateLimitNum 获取全局API速率限制数量
func GetGlobalApiRateLimitNum() int {
	if val := OptionMap["GlobalApiRateLimitNum"]; val != "" {
		if num, err := strconv.Atoi(val); err == nil && num > 0 {
			return num
		}
	}
	return GlobalApiRateLimitNum
}

// GetGlobalApiRateLimitDuration 获取全局API速率限制时间窗口（秒）
func GetGlobalApiRateLimitDuration() int64 {
	if val := OptionMap["GlobalApiRateLimitDuration"]; val != "" {
		if duration, err := strconv.ParseInt(val, 10, 64); err == nil && duration > 0 {
			return duration
		}
	}
	return GlobalApiRateLimitDuration
}

// GetGlobalWebRateLimitNum 获取全局Web速率限制数量
func GetGlobalWebRateLimitNum() int {
	if val := OptionMap["GlobalWebRateLimitNum"]; val != "" {
		if num, err := strconv.Atoi(val); err == nil && num > 0 {
			return num
		}
	}
	return GlobalWebRateLimitNum
}

// GetGlobalWebRateLimitDuration 获取全局Web速率限制时间窗口（秒）
func GetGlobalWebRateLimitDuration() int64 {
	if val := OptionMap["GlobalWebRateLimitDuration"]; val != "" {
		if duration, err := strconv.ParseInt(val, 10, 64); err == nil && duration > 0 {
			return duration
		}
	}
	return GlobalWebRateLimitDuration
}

// GetUploadRateLimitNum 获取上传速率限制数量
func GetUploadRateLimitNum() int {
	if val := OptionMap["UploadRateLimitNum"]; val != "" {
		if num, err := strconv.Atoi(val); err == nil && num > 0 {
			return num
		}
	}
	return UploadRateLimitNum
}

// GetUploadRateLimitDuration 获取上传速率限制时间窗口（秒）
func GetUploadRateLimitDuration() int64 {
	if val := OptionMap["UploadRateLimitDuration"]; val != "" {
		if duration, err := strconv.ParseInt(val, 10, 64); err == nil && duration > 0 {
			return duration
		}
	}
	return UploadRateLimitDuration
}

// GetDownloadRateLimitNum 获取下载速率限制数量
func GetDownloadRateLimitNum() int {
	if val := OptionMap["DownloadRateLimitNum"]; val != "" {
		if num, err := strconv.Atoi(val); err == nil && num > 0 {
			return num
		}
	}
	return DownloadRateLimitNum
}

// GetDownloadRateLimitDuration 获取下载速率限制时间窗口（秒）
func GetDownloadRateLimitDuration() int64 {
	if val := OptionMap["DownloadRateLimitDuration"]; val != "" {
		if duration, err := strconv.ParseInt(val, 10, 64); err == nil && duration > 0 {
			return duration
		}
	}
	return DownloadRateLimitDuration
}

// GetCriticalRateLimitNum 获取关键操作速率限制数量
func GetCriticalRateLimitNum() int {
	if val := OptionMap["CriticalRateLimitNum"]; val != "" {
		if num, err := strconv.Atoi(val); err == nil && num > 0 {
			return num
		}
	}
	return CriticalRateLimitNum
}

// GetCriticalRateLimitDuration 获取关键操作速率限制时间窗口（秒）
func GetCriticalRateLimitDuration() int64 {
	if val := OptionMap["CriticalRateLimitDuration"]; val != "" {
		if duration, err := strconv.ParseInt(val, 10, 64); err == nil && duration > 0 {
			return duration
		}
	}
	return CriticalRateLimitDuration
}
