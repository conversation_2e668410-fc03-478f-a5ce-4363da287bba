{"no_permission_get_same_or_higher_user": "No permission to get user with same or higher role", "invalid_param": "Invalid parameter", "invalid_input": "Invalid input", "no_permission_update_same_or_higher_user": "No permission to update user with same or higher role", "no_permission_promote_user_to_higher_or_equal": "No permission to promote user to higher or equal role", "no_permission_delete_same_or_higher_user": "No permission to delete user with same or higher role", "cannot_create_user_with_higher_or_equal_role": "Cannot create user with higher or equal role", "user_not_found": "User not found", "cannot_disable_root_user": "Cannot disable root user", "cannot_delete_root_user": "Cannot delete root user", "admin_cannot_promote_to_admin": "Admin cannot promote another user to admin", "user_already_admin": "User is already an admin", "cannot_demote_root_user": "Cannot demote root user", "user_already_common": "User is already a common user", "invalid_or_expired_code": "Invalid or expired code", "db_error_checking_token": "Database error checking token: ", "uuid_duplicate_retry": "Please retry, UUID collision occurred!", "get_service_list_failed": "Failed to get service list", "serialize_service_failed": "Failed to serialize service data", "invalid_service_id": "Invalid service ID", "service_not_found": "Service not found", "name_and_display_name_required": "Name and display name are required", "invalid_service_type": "Invalid service type", "create_service_failed": "Failed to create service", "update_service_failed": "Failed to update service", "delete_service_failed": "Failed to delete service", "service_deleted_successfully": "Service deleted successfully", "toggle_service_status_failed": "Failed to toggle service status", "enabled": "Enabled", "disabled": "Disabled", "service_toggle_success": "Service successfully ", "invalid_env_vars_json": "Invalid environment variables format", "source_package_name_required": "Source package name is required", "invalid_request_data": "Invalid request data", "client_type_required": "Client type is required", "client_template_not_found": "Config template for specified client not found", "template_parse_failed": "Template parsing failed", "template_render_failed": "Template rendering failed", "get_template_failed": "Failed to get template", "register_service_failed": "Failed to register service", "check_service_health_failed": "Failed to check service health", "update_service_health_failed": "Failed to update service health status", "service_name_cannot_be_empty": "Service name cannot be empty", "service_name_already_exists": "Service name '%s' already exists, please use a different name", "package_not_found": "Package '%s' does not exist or cannot retrieve package information", "missing_required_env_vars": "Missing required environment variables: %s"}