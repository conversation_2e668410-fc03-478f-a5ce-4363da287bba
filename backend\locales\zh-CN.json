{"no_permission_get_same_or_higher_user": "无权获取同级或更高等级用户的信息", "invalid_param": "无效的参数", "invalid_input": "输入不合法", "no_permission_update_same_or_higher_user": "无权更新同权限等级或更高权限等级的用户信息", "no_permission_promote_user_to_higher_or_equal": "无权将其他用户权限等级提升到大于等于自己的权限等级", "no_permission_delete_same_or_higher_user": "无权删除同权限等级或更高权限等级的用户", "cannot_create_user_with_higher_or_equal_role": "无法创建权限大于等于自己的用户", "user_not_found": "用户不存在", "cannot_disable_root_user": "无法禁用超级管理员用户", "cannot_delete_root_user": "无法删除超级管理员用户", "admin_cannot_promote_to_admin": "普通管理员用户无法提升其他用户为管理员", "user_already_admin": "该用户已经是管理员", "cannot_demote_root_user": "无法降级超级管理员用户", "user_already_common": "该用户已经是普通用户", "invalid_or_expired_code": "验证码错误或已过期", "db_error_checking_token": "数据库检查Token出错: ", "uuid_duplicate_retry": "请重试，系统生成的 UUID 竟然重复了！", "get_service_list_failed": "获取服务列表失败", "serialize_service_failed": "序列化服务数据失败", "invalid_service_id": "无效的服务ID", "service_not_found": "未找到服务", "name_and_display_name_required": "名称和显示名称为必填项", "invalid_service_type": "无效的服务类型", "create_service_failed": "创建服务失败", "update_service_failed": "更新服务失败", "delete_service_failed": "删除服务失败", "service_deleted_successfully": "服务已成功删除", "toggle_service_status_failed": "切换服务状态失败", "enabled": "启用", "disabled": "禁用", "service_toggle_success": "服务已成功", "invalid_env_vars_json": "环境变量格式无效", "source_package_name_required": "源包名称为必填项", "invalid_request_data": "请求数据无效", "client_type_required": "客户端类型为必填项", "client_template_not_found": "未找到指定客户端的配置模板", "template_parse_failed": "模板解析失败", "template_render_failed": "模板渲染失败", "get_template_failed": "获取模板失败", "register_service_failed": "注册服务失败", "check_service_health_failed": "检查服务健康状态失败", "update_service_health_failed": "更新服务健康状态失败", "search_npm_packages_failed": "搜索NPM包失败", "package_name_required": "包名称为必填项", "package_manager_required": "包管理器为必填项", "get_npm_package_details_failed": "获取NPM包详情失败", "unsupported_package_manager": "不支持的包管理器", "get_installed_packages_failed": "获取已安装包列表失败", "no_supported_package_source": "没有支持的包源", "set_env_vars_failed": "设置环境变量失败", "install_npm_package_failed": "安装NPM包失败", "service_added_successfully": "服务已成功添加", "service_installed_successfully": "服务已成功安装", "service_installation_started": "服务安装已开始，正在后台进行", "service_uninstalled_successfully": "服务已成功卸载", "add_service_instance_failed": "添加服务实例失败", "source_type_required": "源类型为必填项", "mcp_service_id_required": "服务ID为必填项", "invalid_source_type": "无效的源类型", "invalid_uninstall_params": "无效的卸载参数", "npx_not_available": "NPX命令不可用，请确保已安装Node.js和NPM", "service_name_cannot_be_empty": "服务名称不能为空或只包含空白字符", "service_name_already_exists": "服务名称 '%s' 已存在，请使用其他名称", "package_not_found": "包 '%s' 不存在或无法获取包信息", "missing_required_env_vars": "缺少必需环境变量: %s"}