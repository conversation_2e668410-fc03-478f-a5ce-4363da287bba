version: '3.8'

services:
  one-mcp:
    image: docker.io/buru2020/one-mcp:latest
    container_name: one-mcp
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./data:/data
    environment:
      - JWT_SECRET=your-very-secret-key
      - SQLITE_PATH=/data/one-mcp.db
      # We are using the GitHub API to find out how many stars npm packages have.
      # get GITHUB_TOKEN from https://github.com/settings/tokens
      # - GITHUB_TOKEN=your-github-token

      # Rate Limit Configuration (optional - can be configured via web interface)
      # Uncomment and modify these values to customize rate limits
      # Very high values optimized for Cherry Studio batch MCP connections
      - GLOBAL_API_RATE_LIMIT_NUM=5000
      - GLOBAL_API_RATE_LIMIT_DURATION=300
      - GLOBAL_WEB_RATE_LIMIT_NUM=5000
      - GLOBAL_WEB_RATE_LIMIT_DURATION=300
      - UPLOAD_RATE_LIMIT_NUM=500
      - UPLOAD_RATE_LIMIT_DURATION=60
      - DOWNLOAD_RATE_LIMIT_NUM=500
      - DOWNLOAD_RATE_LIMIT_DURATION=60
      - CRITICAL_RATE_LIMIT_NUM=1000
      - CRITICAL_RATE_LIMIT_DURATION=1800