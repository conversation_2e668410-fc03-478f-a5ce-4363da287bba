# 速率限制配置指南

本项目支持通过环境变量和Web界面配置API速率限制，以防止429错误并优化性能。

## 环境变量配置

您可以通过以下环境变量来配置速率限制：

### 全局API速率限制
- `GLOBAL_API_RATE_LIMIT_NUM`: 全局API请求数量限制（默认：120）
- `GLOBAL_API_RATE_LIMIT_DURATION`: 全局API时间窗口，单位秒（默认：180）

### 全局Web速率限制
- `GLOBAL_WEB_RATE_LIMIT_NUM`: 全局Web请求数量限制（默认：120）
- `GLOBAL_WEB_RATE_LIMIT_DURATION`: 全局Web时间窗口，单位秒（默认：180）

### 上传速率限制
- `UPLOAD_RATE_LIMIT_NUM`: 上传请求数量限制（默认：20）
- `UPLOAD_RATE_LIMIT_DURATION`: 上传时间窗口，单位秒（默认：60）

### 下载速率限制
- `DOWNLOAD_RATE_LIMIT_NUM`: 下载请求数量限制（默认：20）
- `DOWNLOAD_RATE_LIMIT_DURATION`: 下载时间窗口，单位秒（默认：60）

### 关键操作速率限制
- `CRITICAL_RATE_LIMIT_NUM`: 关键操作请求数量限制（默认：30）
- `CRITICAL_RATE_LIMIT_DURATION`: 关键操作时间窗口，单位秒（默认：1200）

## 使用示例

### Docker Compose
```yaml
version: '3.8'
services:
  one-mcp:
    image: your-image
    environment:
      - GLOBAL_API_RATE_LIMIT_NUM=200
      - GLOBAL_API_RATE_LIMIT_DURATION=300
      - UPLOAD_RATE_LIMIT_NUM=30
      - CRITICAL_RATE_LIMIT_NUM=50
```

### Docker Run
```bash
docker run -d \
  -e GLOBAL_API_RATE_LIMIT_NUM=200 \
  -e GLOBAL_API_RATE_LIMIT_DURATION=300 \
  -e UPLOAD_RATE_LIMIT_NUM=30 \
  your-image
```

### 系统环境变量
```bash
export GLOBAL_API_RATE_LIMIT_NUM=200
export GLOBAL_API_RATE_LIMIT_DURATION=300
./one-mcp
```

## Web界面配置

1. 登录系统并进入 **设置** 页面
2. 滚动到 **速率限制配置** 部分
3. 根据需要调整各项限制值
4. 点击 **保存** 按钮应用配置

## 配置建议

### 解决429错误的推荐配置
如果您遇到频繁的429错误，建议使用以下配置：

```bash
# 更宽松的API限制
GLOBAL_API_RATE_LIMIT_NUM=300
GLOBAL_API_RATE_LIMIT_DURATION=300

# 更宽松的Web限制
GLOBAL_WEB_RATE_LIMIT_NUM=300
GLOBAL_WEB_RATE_LIMIT_DURATION=300

# 适中的上传/下载限制
UPLOAD_RATE_LIMIT_NUM=50
DOWNLOAD_RATE_LIMIT_NUM=50

# 适中的关键操作限制
CRITICAL_RATE_LIMIT_NUM=100
CRITICAL_RATE_LIMIT_DURATION=1800
```

### 高流量环境配置
对于高流量环境，可以使用更高的限制值：

```bash
GLOBAL_API_RATE_LIMIT_NUM=1000
GLOBAL_API_RATE_LIMIT_DURATION=300
GLOBAL_WEB_RATE_LIMIT_NUM=1000
GLOBAL_WEB_RATE_LIMIT_DURATION=300
```

### 安全优先配置
对于安全要求较高的环境，可以使用更严格的限制：

```bash
GLOBAL_API_RATE_LIMIT_NUM=60
GLOBAL_API_RATE_LIMIT_DURATION=300
CRITICAL_RATE_LIMIT_NUM=10
CRITICAL_RATE_LIMIT_DURATION=3600
```

## 配置优先级

配置的优先级从高到低为：
1. Web界面配置（存储在数据库中）
2. 环境变量配置
3. 代码中的默认值

## 注意事项

1. **重启要求**: 环境变量配置需要重启应用才能生效
2. **实时生效**: Web界面配置会立即生效，无需重启
3. **合理设置**: 过低的限制可能影响正常使用，过高的限制可能导致服务器过载
4. **监控建议**: 建议监控API使用情况，根据实际需求调整配置

## 故障排除

### 仍然出现429错误
1. 检查配置是否正确应用
2. 确认轮询间隔是否合理（建议15秒以上）
3. 检查是否有多个客户端同时访问
4. 考虑增加速率限制值

### 性能问题
1. 如果服务器负载过高，适当降低速率限制
2. 监控内存和CPU使用情况
3. 考虑使用Redis缓存来提高性能

## API接口

### 获取当前配置
```bash
GET /api/option/rate_limit
Authorization: Bearer <your-token>
```

### 更新配置
```bash
PUT /api/option/rate_limit
Authorization: Bearer <your-token>
Content-Type: application/json

{
  "GlobalApiRateLimitNum": 200,
  "GlobalApiRateLimitDuration": 300,
  "UploadRateLimitNum": 30
}
```

注意：API接口需要管理员权限。
