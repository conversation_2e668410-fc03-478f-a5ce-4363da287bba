{"app": {"title": "One MCP", "loading": "Loading..."}, "nav": {"dashboard": "Dashboard", "services": "Services", "market": "Service Market", "analytics": "Analytics", "users": "Users", "profile": "Profile", "preferences": "Preferences"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "loginButton": "<PERSON><PERSON>", "logoutButton": "Logout"}, "common": {"save": "Save", "cancel": "Cancel", "close": "Close", "edit": "Edit", "delete": "Delete", "add": "Add", "back": "Back", "next": "Next", "loading": "Loading...", "error": "Error", "success": "Success", "search": "Search", "searching": "Searching...", "configure": "Configure", "enable": "Enable", "disable": "Disable", "enabled": "Enabled", "disabled": "Disabled", "unknown": "Unknown", "refresh": "Refresh", "uninstall": "Uninstall"}, "batchImport": {"title": "Batch Import Services", "placeholder": "{\n  \"mcpServers\": {\n    \"my-stdio-service\": {\n      \"command\": \"npx\",\n      \"args\": [\"-y\", \"some-package\"],\n      \"env\": {\"API_KEY\": \"your_key\"}\n    },\n    \"my-sse-service\": {\n      \"url\": \"http://example.com/events/sse\"\n    },\n    \"my-http-service\": {\n      \"url\": \"http://example.com/mcp?key=your_key\"\n    }\n  }\n}", "import": "Import", "importing": "Importing...", "invalidJsonError": "Invalid JSON format. Please check your input.", "processingServices": "Processing services...", "importComplete": "Import Complete", "importSummary": "Import completed: {{success}} successful, {{skipped}} skipped, {{failed}} failed.", "successMessage": "Services have been imported successfully and the service list has been refreshed."}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to One MCP", "description": "Your centralized proxy for Model Context Protocol (MCP) services", "content": "Manage your MCP services, monitor performance, and configure them through a unified proxy interface.", "activeServices": "Active Services", "activeServicesDesc": "Number of enabled services", "todayRequests": "Today's Requests", "todayRequestsDesc": "Total API requests today", "avgResponseTime": "Avg. Response Time", "avgResponseTimeDesc": "Today's average response time", "remainingCredits": "Remaining Credits", "remainingCreditsDesc": "Credits feature coming soon", "systemStatus": "System Status", "systemStatusDesc": "System runtime and service health overview", "systemUptime": "System Uptime", "totalServices": "Total Services", "healthyServices": "Healthy Services", "unhealthyServices": "Unhealthy Services", "enabled": "enabled", "quickActions": "Quick Actions", "quickActionsDesc": "Get started with your MCP platform", "manageServices": "Manage Services", "manageServicesDesc": "Configure existing services", "viewAnalytics": "View Analytics", "viewAnalyticsDesc": "Check performance metrics", "userSettings": "User Settings", "userSettingsDesc": "Manage your account", "installService": "Install Service", "installServiceDesc": "Add new MCP services", "recentActivity": "Recent Activity", "recentActivityDesc": "Latest actions and system events (Static data, API in development)", "time": "Time", "event": "Event", "service": "Service", "status": "Status", "serviceStarted": "Service started", "configUpdated": "Config updated", "apiKeyGenerated": "API key generated", "serviceRestarted": "Service restarted", "searchService": "Search Service", "analytics": "Analytics", "system": "System", "userManagement": "User Management", "success": "Success", "warning": "Warning", "today": "Today", "yesterday": "Yesterday", "unknown": "Unknown", "error": "Error", "fetchDataFailed": "Failed to fetch data"}, "clipboardError": {"accessDenied": "Cannot access clipboard. Please ensure the website is accessed via HTTPS, or allow clipboard access in browser settings.", "execCommandFailed": "Copy function is not available. Please manually select and copy the text.", "notSupported": "Your browser does not support clipboard operations. Please manually select and copy the text."}, "services": {"title": "MCP Services", "description": "Description", "addService": "Add Service", "batchImport": "Batch Import", "fromMarket": "Install from Market", "customInstall": "Custom Install", "noServicesFound": "No services found.", "viewGrid": "Grid View", "viewList": "List View", "allServices": "All Services", "active": "Active", "inactive": "Inactive", "mcpServices": "MCP Services", "manageAndConfigure": "Manage and configure your multi-cloud platform services", "installFromMarket": "Install from Market", "status": "Status", "serviceName": "Service Name", "version": "Version", "healthStatus": "Health Status", "enabledStatus": "Enabled Status", "operations": "Actions", "configure": "Configure", "uninstall": "Uninstall", "refreshHealthStatus": "Refresh health status", "uninstallService": "Uninstall service", "dailyRequests": "Daily Requests", "requestsRemaining": "requests remaining", "confirmUninstall": "Confirm Uninstall", "confirmUninstallDescription": "Are you sure you want to uninstall this service? This will remove all related configurations.", "cancel": "Cancel"}, "market": {"title": "Service Marketplace", "description": "Discover and install MCP services from various sources", "searchPlaceholder": "Search for MCP services...", "search": "Search", "searching": "Searching...", "noServicesFound": "No services found. Try a different search term.", "searchingServices": "Searching for services...", "backToMarketplace": "Back to Marketplace", "serviceNotFound": "Service Not Found", "serviceNotFoundDesc": "The requested service could not be found.", "install": "Install", "installing": "Installing...", "installed": "Installed", "version": "Version", "author": "Author", "stars": "Stars", "downloads": "Downloads", "weeklyDownloads": "Weekly Downloads"}, "serviceMarketplace": {"title": "Service Marketplace", "description": "Discover and install MCP services from various sources", "searchPlaceholder": "Search for MCP services...", "searchButton": "Search", "searching": "Searching for services...", "noServicesFound": "No services found. Try a different search term.", "installing": "Installing...", "installationComplete": "Installation complete", "installationFailed": "Installation failed"}, "serviceDetails": {"loadingServiceDetails": "Loading service details...", "backToMarketplace": "Back to Marketplace", "serviceNotFound": "Service Not Found", "serviceNotFoundDescription": "The requested service could not be found.", "by": "By", "source": "Source", "uninstallService": "Uninstall Service", "installService": "Install Service", "installingService": "Installing Service...", "installing": "Installing", "from": "from", "theServiceWasInstalledSuccessfully": "The service was installed successfully", "thereWasAProblemDuringInstallation": "There was a problem during installation", "finish": "Finish", "close": "Close"}, "users": {"title": "User Management", "description": "Manage system users and their permissions", "searchPlaceholder": "Search users...", "addUser": "Add User", "noUsersFound": "No users found.", "id": "ID", "username": "Username", "displayName": "Display Name", "email": "Email", "userRole": "User Role", "binding": "Binding", "status": "Status", "actions": "Actions", "loading": "Loading...", "search": "Search", "searching": "Searching...", "edit": "Edit", "delete": "Delete", "promoteToAdmin": "Set as <PERSON><PERSON>", "demoteToUser": "Set as User", "showing": "Showing", "users": "users", "previousPage": "Previous", "nextPage": "Next", "confirmDeleteTitle": "Confirm Delete", "confirmDeleteDescription": "Are you sure you want to delete this user? This action cannot be undone.", "cancel": "Cancel", "roles": {"admin": "Administrator", "user": "User"}, "statusTypes": {"active": "Active", "inactive": "Inactive"}, "messages": {"deleteSuccess": "User deleted successfully", "deleteFailed": "Failed to delete user", "promoteSuccess": "User promoted to administrator", "demoteSuccess": "User demoted to regular user", "enableSuccess": "User enabled", "disableSuccess": "User disabled", "operationFailed": "Operation failed"}, "bindings": {"github": "GitHub", "google": "Google", "wechat": "WeChat", "none": "None"}}, "usersPage": {"title": "User Management", "description": "Manage system user accounts", "searchPlaceholder": "Search by user ID, username, display name or email...", "addUser": "Add User", "noUsersFound": "No users found", "id": "ID", "username": "Username", "displayName": "Display Name", "email": "Email", "userRole": "User Role", "binding": "Binding", "status": "Status", "actions": "Actions", "loading": "Loading...", "search": "Search", "searching": "Searching...", "edit": "Edit", "delete": "Delete", "promoteToAdmin": "Set as <PERSON><PERSON>", "demoteToUser": "Set as User", "showing": "Showing", "users": "users", "previousPage": "Previous", "nextPage": "Next", "confirmDeleteTitle": "Confirm Delete", "confirmDeleteDescription": "Are you sure you want to delete this user? This action cannot be undone.", "cancel": "Cancel", "roles": {"superAdmin": "Super Admin", "admin": "Admin", "user": "User", "unknown": "Unknown"}, "bindings": {"none": "None"}, "messages": {"operationSuccess": "Operation Successful", "operationFailed": "Operation Failed", "deleteSuccess": "Delete Successful", "deleteFailed": "Delete Failed", "userDeleted": "User deleted successfully", "userPromoted": "User promoted to admin", "userDemoted": "User demoted to regular user", "userEnabled": "User enabled", "userDisabled": "User disabled", "unknownError": "Unknown error", "networkError": "Network error"}}, "userDialog": {"addTitle": "Add User", "editTitle": "Edit User", "addDescription": "Create a new user account.", "editDescription": "Modify the user's username, display name and password.", "form": {"username": "Username", "displayName": "Display Name", "password": "Password", "passwordPlaceholder": "Enter password", "passwordEditPlaceholder": "Leave empty to keep current password"}, "actions": {"cancel": "Cancel", "save": "Save", "saving": "Saving..."}, "messages": {"createSuccess": "Create Successful", "updateSuccess": "Update Successful", "createFailed": "Create Failed", "updateFailed": "Update Failed", "userCreated": "User created successfully", "userUpdated": "User information updated", "unknownError": "Unknown error", "networkError": "Network error"}}, "profile": {"title": "Profile", "description": "Manage your personal information and account settings", "personalInfo": "Personal Information", "personalInfoDesc": "Update your personal details", "accountSecurity": "Account Security", "accountSecurityDesc": "Manage your password and API access", "apiAccess": "API Access", "apiAccessDesc": "Manage your API key for external access", "form": {"username": "Username", "email": "Email", "displayName": "Display Name", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "apiKey": "API Key", "loginMethod": "Login Method"}, "loginMethods": {"password": "Password", "github": "GitHub", "google": "Google", "wechat": "WeChat"}, "actions": {"saveProfile": "Save Profile", "changePassword": "Change Password", "refreshApiKey": "Refresh API Key", "showApiKey": "Show API Key", "hideApiKey": "Hide API Key", "saving": "Saving..."}, "messages": {"profileUpdateSuccess": "Profile updated successfully", "profileUpdateFailed": "Failed to update profile", "passwordChangeSuccess": "Password changed successfully", "passwordChangeFailed": "Failed to change password", "passwordMismatch": "New password and confirm password do not match", "currentPasswordIncorrect": "Current password is incorrect", "oauthUserCannotChangePassword": "OAuth users cannot change password", "apiKeyRefreshSuccess": "API key refreshed successfully", "apiKeyRefreshFailed": "Failed to refresh API key", "fetchUserInfoFailed": "Failed to fetch user information"}, "notes": {"oauthPasswordNote": "OAuth users cannot change their password. Please use your OAuth provider to manage your account.", "oauthReadOnlyInfo": "Third-party login user information (read-only)", "apiKeyTitle": "Important Notes", "apiKeyNote1": "After regeneration, the old API Key will be immediately invalidated", "apiKeyNote2": "Please update your application configuration promptly", "apiKeyNote3": "Please keep your API Key safe"}}, "preferences": {"title": "Preferences", "description": "Manage your system configuration and login options", "general": "General Settings", "generalDesc": "Configure basic system settings", "oauth": "OAuth Configuration", "oauthDesc": "Configure third-party login providers", "github": "Configure GitHub OAuth App", "google": "Configure Google OAuth App", "githubDesc": "To support login and registration via GitHub", "githubLinkText": "Click here to manage your GitHub OAuth App", "googleDesc": "To support login and registration via Google", "googleLinkText": "Click here to manage your Google OAuth App", "form": {"serverAddress": "Server Address", "githubClientId": "GitHub Client ID", "githubClientSecret": "GitHub Client Secret", "googleClientId": "Google Client ID", "googleClientSecret": "Google Client Secret", "enableGithubOAuth": "Allow login & registration via GitHub account", "enableGoogleOAuth": "Allow login & registration via Google account", "githubClientIdPlaceholder": "Enter GitHub Client ID", "githubClientSecretPlaceholder": "Enter GitHub Client Secret", "googleClientIdPlaceholder": "Enter Google Client ID", "googleClientSecretPlaceholder": "Enter Google Client Secret"}, "actions": {"save": "Save", "saving": "Saving..."}, "instructions": {"githubHomepage": "Homepage URL fill", "githubCallback": ", Authorization callback URL fill", "googleOrigins": "Authorized JavaScript origins fill", "googleRedirect": ", Authorized redirect URIs fill"}, "messages": {"saveSuccess": "Setting<PERSON> saved successfully", "saveFailed": "Failed to save settings", "githubOAuthEnabled": "GitHub OAuth enabled", "githubOAuthDisabled": "GitHub OAuth disabled", "googleOAuthEnabled": "Google OAuth enabled", "googleOAuthDisabled": "Google OAuth disabled", "githubOAuthSaved": "GitHub OAuth configuration saved", "googleOAuthSaved": "Google OAuth configuration saved"}}, "analytics": {"title": "Analytics", "description": "Monitor system performance and usage statistics", "usageStatistics": "Usage Statistics", "serviceUtilization": "Service Utilization", "aggregatedPerformanceOverview": "Aggregated performance overview for enabled services.", "sortedBy": "Sorted by", "todayRequests": "Today's Requests", "highToLow": "high to low", "lowToHigh": "low to high", "clickColumnHeader": "Click the column header to", "changeSortOrder": "change sort order.", "loadingStatistics": "Loading statistics...", "error": "Error", "summaryOfServiceUsage": "A summary of your service usage.", "service": "Service", "status": "Status", "todayAvgLatency": "Today's Avg. Latency (ms)", "noEnabledServicesWithUsageStatisticsAvailableYet": "No enabled services with usage statistics available yet.", "enabled": "Enabled"}, "language": {"select": "Select Language", "current": "Current Language"}, "envVarModal": {"title": "Fill Required Environment Variables", "description": "Installing this service requires the following environment variables. Please complete them to continue.", "placeholder": "Please enter {{varName}}", "errorRequired": "Please fill in {{varName}}", "cancel": "Cancel", "confirm": "Confirm"}, "customServiceModal": {"title": "Create Custom Service", "description": "Fill in the information below to create a custom MCP service", "form": {"serviceName": "Service Name", "serviceNamePlaceholder": "Enter service name", "serviceType": "Service Type", "serviceTypePlaceholder": "Select service type", "command": "Execute Command", "commandPlaceholder": "npx or uvx", "arguments": "Command Arguments", "argumentsPlaceholder": "--arg1 value1\n--arg2 value2", "environments": "Environment Variables", "environmentsPlaceholder": "VARIABLE_NAME=value\nANOTHER_VAR=another_value", "serverUrl": "Server URL", "serverUrlPlaceholder": "https://your-server.com/api", "requestHeaders": "Request Headers", "requestHeadersPlaceholder": "Content-Type=application/json\nAuthorization=Bearer token\n..."}, "serviceTypes": {"stdio": "Standard Input/Output (stdio)", "sse": "Server-Sent Events (sse)", "streamableHttp": "Streamable HTTP (streamableHttp)"}, "status": {"validating": "Validating", "validatingDescription": "Checking form data...", "validationSuccess": "Validation Success!", "validationSuccessDescription": "Creating service...", "creating": "Creating", "creatingDescription": "Saving service configuration..."}, "actions": {"cancel": "Cancel", "createService": "Create Service", "validating": "Validating...", "validationSuccess": "Validation Success", "creating": "Creating..."}, "messages": {"createSuccess": "Create Success", "createSuccessDescription": "Service {{serviceName}} has been successfully created", "createFailed": "Create Failed", "unknownError": "Unknown error", "commandMustStartWith": "Command must start with npx or uvx", "installationSubmitted": "Installation Submitted", "installationSubmittedDescription": "Installation task for service {{serviceName}} has been submitted, please wait", "installationSuccess": "Installation Success", "installationSuccessDescription": "Service {{serviceName}} has been successfully installed", "installationFailed": "Installation Failed", "installationFailedDescription": "Service {{serviceName}} installation failed: {{error}}", "installationTimeout": "Installation Timeout", "installationTimeoutDescription": "Service {{serviceName}} installation timed out, please check status manually", "statusCheckTimeout": "Status Check Timeout", "statusCheckTimeoutDescription": "Unable to get installation status for service {{serviceName}}", "statusCheckFailed": "Status Check Failed", "statusCheckFailedDescription": "Error occurred while checking installation status for service {{serviceName}}", "parseCommandFailed": "Unable to parse package manager or package name from command. Command must be \"npx\" or \"uvx\", and package name must be specified in command arguments."}}, "serviceConfigModal": {"title": "Service Configuration", "description": "Configure your service settings and view endpoint details.", "actions": {"save": "Save", "saving": "Saving...", "close": "Close", "copySSEConfig": "Copy SSE Config", "copyHTTPConfig": "Copy HTTP Config", "copyConfigLabel": "Copy Endpoint Config", "copyHeaderText": "<PERSON><PERSON> Header Text", "limitPlaceholder": "Enter limit (0 for unlimited)"}, "sections": {"environmentVariables": "Environment Variables", "noEnvironmentVariables": "No environment variables configured for this service.", "serviceEndpoints": "Service Endpoints", "urlPlaceholder": "Endpoint URL will appear here", "sseConfigJson": "SSE Configuration (JSON)", "httpConfigJson": "HTTP Configuration (JSON)", "rpdLimit": "Daily Request Limit (RPD)", "currentLimit": "Current limit:", "requestsPerDay": "requests/day", "unlimited": " (Unlimited)"}, "messages": {"saveFailed": "Failed to save environment variable.", "sseConfigCopied": "SSE config copied", "sseConfigCopiedDesc": "The SSE client configuration has been copied to your clipboard.", "httpConfigCopied": "HTTP config copied", "httpConfigCopiedDesc": "The HTTP client configuration has been copied to your clipboard.", "headerCopied": "Header copied to clipboard", "copyFailed": "Failed to copy to clipboard.", "manualCopyHint": "Clipboard access might be restricted. Please select and copy the text manually.", "selectAllHint": "Click to select all and press Ctrl+C (or Cmd+C) to copy.", "clipboardNotSupported": "Clipboard access is not available in this browser or context (e.g. HTTP).", "updateSuccess": "Update successful", "updateFailed": "Update failed", "rpdLimitUpdated": "RPD limit updated to {limit}.", "rpdUpdateError": "An unexpected error occurred while updating the RPD limit.", "unlimitedValue": "Unlimited"}}}